import './global.css';
import { RootProvider } from 'fumadocs-ui/provider';
import { Inter } from 'next/font/google';
import type { ReactNode } from 'react';
import AmplitudeProvider from '@/components/AmplitudeProvider'

const inter = Inter({
  subsets: ['latin'],
});

export const metadata = {
  icons: {
    icon: '/icon.png',
  },
};

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <html lang="zh-CN" className={inter.className} suppressHydrationWarning>
      <body className="flex flex-col min-h-screen">
        <RootProvider>
          <AmplitudeProvider />
          {children}
        </RootProvider>
      </body>
    </html>
  );
}
